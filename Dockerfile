# Usar imagem Python otimizada
FROM python:3.9-slim

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Configurar diretório de trabalho
WORKDIR /code

# 
COPY ./requirements.txt /code/requirements.txt

# 
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# 
COPY ./app /code/app

COPY ./gunicorn_config.py /code/gunicorn_config.py

ENV PYTHONPATH="/code:${PYTHONPATH}"

# Configurações de sistema para estabilidade
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Mude para o diretório da aplicação
WORKDIR /code

# Comando otimizado com configurações específicas
CMD ["gunicorn", "--config", "gunicorn_config.py", "app.relatorio_api:app"]