# gunicorn_config.py
import multiprocessing
import os

# Configurações básicas - REDUZIDAS
bind = "0.0.0.0:8000"
workers = int(os.getenv("WORKERS", 3))  # ← REDUZIDO: 3 workers apenas
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 400  # ← REDUZIDO: 500 conexões

# Timeouts
timeout = 10 # ← REDUZIDO: 1 minuto
keepalive = 30  # ← REDUZIDO: 15 segundos
graceful_timeout = 20

# Reciclagem mais agressiva para liberar memória
max_requests = 800  # ← REDUZIDO: recicla workers mais cedo
max_requests_jitter = 80
preload_app = True

# Configuração de memória
worker_tmp_dir = "/dev/shm"  # Usar RAM compartilhada

# Logs
accesslog = "-"
errorlog = "-"
loglevel = "info"  # ← Menos logs para economizar recursos