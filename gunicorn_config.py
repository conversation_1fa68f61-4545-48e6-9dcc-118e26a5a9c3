# gunicorn_config.py
import multiprocessing
import os

# Configurações básicas - OTIMIZADAS
bind = "0.0.0.0:8000"
workers = int(os.getenv("WORKERS", 2))  # 2 workers para evitar sobrecarga
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 200  # Reduzido para evitar timeout

# Timeouts - AUMENTADOS para queries longas
timeout = 300  # 5 minutos para queries pesadas
keepalive = 60  # 1 minuto para manter conexões
graceful_timeout = 60  # 1 minuto para shutdown graceful

# Reciclagem otimizada para estabilidade
max_requests = 1000  # Aumentado para reduzir overhead de reciclagem
max_requests_jitter = 100
preload_app = True  # Manter para melhor performance

# Configurações específicas para queries longas
worker_tmp_dir = "/dev/shm"  # Usar RAM compartilhada
limit_request_line = 8192  # Aumentar limite de linha de request
limit_request_fields = 200  # Aumentar limite de campos
limit_request_field_size = 16384  # Aumentar tamanho dos campos

# Logs
accesslog = "-"
errorlog = "-"
loglevel = "info"

# Configurações de processo para estabilidade
def when_ready(server):
    """Callback executado quando o servidor está pronto"""
    server.log.info("🚀 Servidor Gunicorn pronto para receber requests")

def worker_int(worker):
    """Callback para interrupção de worker"""
    worker.log.info(f"⚠️ Worker {worker.pid} interrompido")

def on_exit(server):
    """Callback na saída do servidor"""
    server.log.info("🛑 Servidor Gunicorn finalizado")