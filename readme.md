
## para parar o container
docker stop relatorio

## para remover o container
docker rm relatorio

## para remover a imagem
docker rmi relatorio --force

## para criar a image 
docker build -t relatorio .

## para logar no ECR repositorios de imagens do container (lembra de colocar as credentais ~/.aws/config)
 aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 895525808331.dkr.ecr.us-west-2.amazonaws.com

## para tagear a imagem 
docker tag relatorio:latest 895525808331.dkr.ecr.us-west-2.amazonaws.com/avaliativa:relatorio

## para subit image no ECR
 docker push 895525808331.dkr.ecr.us-west-2.amazonaws.com/avaliativa:relatorio




